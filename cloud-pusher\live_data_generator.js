// import metadata from '../../data/metadata.json' with { type: 'json' };

function generateLiveData(metadata) {

    let liveDataArray = [Math.floor(Date.now() / 1000)] // live timestamp 

    if (Array.isArray(metadata.registers)) {

        for (let register of metadata.registers) {

            let value

            // empty register 
            if (!register || !register.dtype) {
                value = null
                liveDataArray.push(value)
                continue
            }

            switch (register.dtype) {
                case 'F32': // 32-bit float
                case 'F64': // 64-bit float 
                    const decimalPlaces = register.display === undefined ? 2 : register.display
                    value = parseFloat((Math.random() * 100).toFixed(decimalPlaces))
                    break
                case 'B': // bool as 0 or 1
                    value = Math.round(Math.random()); // 0 or 1
                    break;
                case 'STR':
                    value = `Value_${Math.floor(Math.random() * 100)}`;
                    break;
                default:
                    // float with 2 decimal places 
                    value = parseFloat((Math.random() * 100).toFixed(2))
                    break
            }
            liveDataArray.push(value)
        }
    } else {
        console.error("ERROR invalid metadata")
    }
    return liveDataArray 

}

// let val = generateLiveData(metadata)
// console.log(val)

export {generateLiveData}