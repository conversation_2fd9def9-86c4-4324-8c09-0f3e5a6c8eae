import db from '../../../database/db.js'
import transporter from '../../../utils/emailTransporter.js'
import bcrypt from 'bcryptjs'
import { createResponse } from '../../../utils/stdResponse.js'
import { testRule, userRules } from '../../../../../shared/rules/validation.js'
import { assignUserToOrgByEmailDomain } from '../../../utils/emailDomain.js'


export default async function registrationRoutes(fastify, options) {
    fastify.post('/api/app/register', async (request, reply) => {

        const { data } = request.body || {};
        if (!data || typeof data !== 'object') {
            return reply.status(400).send({ error: 'Invalid request format' });
        }

        const { first = '', last = '', companyName = '', email = '', pass = '' } = data;
        const errors = {};

        const firstRules = userRules.name; // [required, onlyLetters]
        const lastRules = userRules.name;  // [required, onlyLetters]
        const companyNameRules = userRules.companyName; // [required]

        // validate fields, populate errors object
        const firstValidation = testRule(firstRules, first);
        if (firstValidation !== true) errors.first = firstValidation;

        const lastValidation = testRule(lastRules, last);
        if (lastValidation !== true) errors.last = lastValidation;

        const emailValidation = testRule(userRules.email, email);
        if (emailValidation !== true) errors.email = emailValidation;

        const passValidation = testRule(userRules.password, pass);
        if (passValidation !== true) errors.pass = passValidation;

        const companyNameValidation = testRule(companyNameRules, companyName);
        if (companyNameValidation !== true) errors.companyName = companyNameValidation;

        if (Object.keys(errors).length > 0) {
            return reply.status(200).send(createResponse({ errors }));
        }



        try {
            const saltRounds = 10;
            // const hashedPassword = await bcrypt.hash(pass, saltRounds);  // hashing deactivated for testing

            const userData = {              // database insertion
                first: first.trim(),
                last: last.trim(),
                email: email.toLowerCase().trim(),
                pass: pass.trim(),
                company_name: companyName.trim(),
                registered: false,
            };

            const [userId] = await db('users').insert(userData).returning('uid');
            const user = { id: userId, ...userData, pass: undefined };

            const token = fastify.jwt.sign({ email }, { expiresIn: '24h' });     // expiration 24hrs

            console.log("sending email, using node env :::::::::::::::::::::: ", process.env.NODE_ENV)
            console.log(token)  // debug

            await transporter.sendMail({
                from: '"Praevista" <<EMAIL>>',
                to: email,
                subject: 'Verify Your Account',
                html:
                    `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
                        <div style="background: #f8f9fa; padding: 20px; border-bottom: 3px solid #4a6baf;">
                            <h1 style="color: #2c3e50; margin: 0;">Welcome to Praevista</h1>
                        </div>

                        <div style="padding: 25px; line-height: 1.6;">
                            <p>Thank you for registering for a Praevista account. To complete your registration, please verify your email address.</p>

                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${process.env.SERVER_INSTANCE || "http://127.0.0.1:9000"}/api/app/verify?token=${token}"
                                style="background-color: #4a6baf; color: white; padding: 12px 25px;
                                        text-decoration: none; border-radius: 4px; font-weight: bold;
                                        display: inline-block;">
                                    Verify Your Account
                                </a>
                            </div>

                            <p style="font-size: 14px; color: #666;">
                                Or copy and paste this link into your browser:<br>
                                <span style="word-break: break-all;">${process.env.SERVER_INSTANCE || "http://127.0.0.1:9000"}/api/app/verify?token=${token}</span>
                            </p>

                            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">

                            <p style="font-size: 13px; color: #777;">
                                <strong>Note:</strong> This verification link will expire in 24 hours. If you didn't create an account with Praevista,
                                please ignore this email. <br>
                                Contact <a href="mailto:<EMAIL>" style="color: #4a6baf;"><EMAIL></a>
                                if you have questions.
                            </p>
                            <p style="text-align: center; margin-top: 30px; color: #4a6baf; font-weight: bold;">
                                Thanks from the Praevista Team!
                            </p>
                        </div>
                    </div>
                `
            });
            return reply.status(200).send(
                createResponse({
                    data: user,
                    other: { message: 'Registration successful. Check your email for verification.' },
                })
            );
        } catch (error) {

            if (error.code === 'SQLITE_CONSTRAINT') {
                return reply.status(200).send(
                    createResponse({
                        errors: { email: 'Email already exists' },
                    })
                );
            } else if (error.message.includes('Failed to send email')) {
                return reply.status(503).send({ error: 'Email service unavailable' });
            } else {
                server.log.error(error);
                return reply.status(500).send(createResponse({ error: 'Internal server error' }));
            }
        }
    })


    fastify.get('/api/app/verify', async (request, reply) => {
        const { token } = request.query
        if (!token) {
            return reply.code(400).send({ error: 'Token required' })
        }
        try {
            const decoded = fastify.jwt.verify(token)
            const { email } = decoded
            const user = await db('users').where({ email }).first()

            if (!user) {
                return reply.code(404).send({ error: 'User not found' })
            }

            await db('users').where({ email }).update({ registered: true })

            // assign user to an organization based on email domain
            try {
                await assignUserToOrgByEmailDomain(db, user.uid, email);
                // if no matching organization is found, the user remains unassigned
            } catch (assignError) {
                fastify.log.error('Error assigning user to organization:', assignError);
            }

            return reply.redirect(process.env.CLIENT_REDIRECT)

        } catch (err) {
            fastify.log.error(err)
            if (err.name === 'JsonWebTokenError') {
                return reply.code(400).send(createResponse({ error: 'Invalid token' }))
            } else if (err.name === 'TokenExpiredError') {
                return reply.code(400).send(createResponse({ error: 'Expired token' }))
            } else {
                return reply.code(500).send(createResponse({ error: 'Internal Server Error' }))
            }
        }
    })

}
