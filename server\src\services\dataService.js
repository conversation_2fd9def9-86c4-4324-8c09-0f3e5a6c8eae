import { join } from 'path';
import fs from 'fs';
import { DtypeMapper } from '../utils/dtypeMapper.js';

export class DataService {
    constructor(knex, deviceService, logger) {
        this.knex = knex;
        this.deviceService = deviceService;
        this.logger = logger;
        this.dataDir = join(process.cwd(), 'data', 'devices');

        //  ensure the data directory exists
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
    }

    /**
     * filter out empty or invalid registers from metadata
     * @param {object} metadata - Device metadata
     * @returns {object} - Cleaned metadata
     */
    cleanMetadata(metadata) {
        if (!metadata) return { registers: [] };

        // copy : avoid modifying the original
        const cleanedMetadata = { ...metadata };

        // validate registers array
        if (!Array.isArray(cleanedMetadata.registers)) {
            cleanedMetadata.registers = [];
            return cleanedMetadata;
        }

        // process all registers, including empty objects
        const validRegisters = [];
        cleanedMetadata.registers.forEach((register, index) => {
            // Handle null or non-object registers
            if (!register || typeof register !== 'object') {
                console.log(`Creating placeholder for null or non-object register at index ${index}`);
                // placeholder register with null dtype
                register = {
                    index: index,
                    dtype: null,
                    isEmptyRegister: true
                };
                validRegisters.push(register);
                return;
            }

            // handle empty objects - preserve but mark as empty
            if (Object.keys(register).length === 0) {
                console.log(`Found empty object register at index ${index}, preserving with null dtype`);
                register.index = index;
                register.dtype = null;
                register.isEmptyRegister = true;
                validRegisters.push(register);
                return;
            }

            // handle registers without dtype
            if (!register.dtype) {
                console.log(`Register at index ${index} has no dtype, setting default dtype 'string'`);
                register.dtype = 'string';
            }

            // add index property for reference
            register.index = index;
            validRegisters.push(register);
        });

        // replace the registers array with the filtered one
        cleanedMetadata.registers = validRegisters;
        console.log(`Cleaned metadata: ${validRegisters.length} valid registers out of ${metadata.registers.length} total`);

        return cleanedMetadata;
    }

    /**
     * log register details for debugging
     * @param {Array} registers - Array of register objects
     */
    logRegisterDetails(registers) {
        if (!registers || !Array.isArray(registers)) {
            console.log('No registers to log');
            return;
        }

        console.log(`Register details (${registers.length} registers):`);
        registers.forEach((reg, idx) => {
            if (!reg || typeof reg !== 'object') {
                console.log(`  ${idx}: <invalid register>`);
                return;
            }

            const name = reg.register || reg.name || `unnamed_${idx}`;
            const dtype = reg.dtype || 'unknown';
            console.log(`  ${idx}: ${name} (${dtype})`);
        });
    }

    async ensureDataTableExists(mac, metadata) {
        try {
            console.log(`Ensuring data table exists for device: ${mac}`);

            const cleanedMetadata = this.cleanMetadata(metadata);

            const deviceKnex = await this.getDeviceConnection(mac);
            const tableName = 'data';

            const exists = await deviceKnex.schema.hasTable(tableName);
            if (!exists) {
                console.log(`Creating data table for device: ${mac}`);
                await deviceKnex.schema.createTable(tableName, table => {
                    table.integer('timestamp').notNullable();

                    cleanedMetadata.registers.forEach((register) => {
                        const columnName = `channel_${register.index}`;

                        // empty registers (with null dtype)
                        if (register.isEmptyRegister || register.dtype === null) {
                            console.log(`Creating column ${columnName} for empty register with null dtype`);
                            table.text(columnName);
                            return;
                        }

                        console.log(`Creating column ${columnName} with type ${register.dtype}`);

                        try {
                            // normalize for consistent comparison
                            const dtype = register.dtype.toLowerCase();

                            switch (dtype) {
                                case 'float':
                                case 'f32':
                                case 'f64':
                                    table.float(columnName);
                                    break;
                                case 'int':
                                case 'i32':
                                case 'i16':
                                case 'i8':
                                    table.integer(columnName);
                                    break;
                                case 'str':
                                case 'string':
                                    table.string(columnName);
                                    break;
                                case 'b':
                                case 'bool':
                                case 'boolean':
                                    table.boolean(columnName);
                                    break;
                                default:
                                    console.log(`Unknown data type: ${register.dtype}, defaulting to text for column ${columnName}`);
                                    table.text(columnName);
                            }
                        } catch (error) {
                            console.error(`Error creating column ${columnName}:`, error);
                            table.string(columnName);
                        }
                    });

                    table.index(['timestamp']);
                });
                console.log(`Data table created successfully for device: ${mac}`);
            } else {
                console.log(`Data table already exists for device: ${mac}`);
            }

            return tableName;
        } catch (error) {
            console.error(`Error creating device database for ${mac}:`, error);
            throw error;
        }
    }

    async logData(mac, defaultTimestamp, dataPayload) {
        try {
            // get device metadata
            const device = await this.deviceService.findDeviceByMac(mac);
            if (!device) {
                throw new Error(`Device not found: ${mac}`);
            }

            // Get device metadata from the device object
            let metadata;
            try {
                metadata = typeof device.meta === 'string' ? JSON.parse(device.meta) : device.meta;
            } catch (e) {
                console.error(`Error parsing device metadata: ${e.message}`);
                throw new Error(`Invalid device metadata format for device ${mac}`);
            }

            // Ensure the data table exists for this device
            await this.ensureDataTableExists(mac, metadata);

            // Get database connection
            const deviceKnex = await this.getDeviceConnection(mac);
            if (!deviceKnex) {
                throw new Error(`Failed to get database connection for device ${mac}`);
            }

            // Clean metadata - use the appropriate method
            const cleanedMetadata = this.cleanMetadata ?
                this.cleanMetadata(metadata) :
                (this.deviceService.cleanMetadata ?
                    this.deviceService.cleanMetadata(metadata) :
                    metadata);

            console.log(`Using cleaned metadata with ${cleanedMetadata.registers?.length || 0} registers`);

            // Handle array of arrays format (new format)
            if (Array.isArray(dataPayload) && dataPayload.length > 0 && Array.isArray(dataPayload[0])) {
                console.log(`Processing ${dataPayload.length} rows of array data for device ${mac}`);
                const dataRows = [];

                for (const row of dataPayload) {
                    if (!row || !Array.isArray(row) || row.length === 0) {
                        console.warn('Skipping invalid row:', row);
                        continue;
                    }

                    // First element is timestamp
                    const rowTimestamp = row[0] || defaultTimestamp;
                    if (!rowTimestamp) {
                        console.warn('Skipping row with missing timestamp');
                        continue;
                    }

                    const data = { timestamp: rowTimestamp };

                    // Map remaining elements to register columns
                    for (let i = 0; i < cleanedMetadata.registers.length && i + 1 < row.length; i++) {
                        const register = cleanedMetadata.registers[i];
                        const columnName = `channel_${register.index}`;
                        const value = row[i + 1];

                        // Format value based on data type if needed
                        if (DtypeMapper && DtypeMapper.formatValueForSQL) {
                            data[columnName] = register.dtype
                                ? DtypeMapper.formatValueForSQL(value, register.dtype)
                                : value;
                        } else {
                            // Fallback if DtypeMapper is not available
                            data[columnName] = value;
                        }
                    }

                    // Log if we're dropping excess values
                    if (row.length - 1 > cleanedMetadata.registers.length) {
                        console.warn(`Dropping ${row.length - 1 - cleanedMetadata.registers.length} excess values from row with timestamp ${rowTimestamp}`);
                    }

                    // Log if we're filling missing values with null
                    if (row.length - 1 < cleanedMetadata.registers.length) {
                        console.log(`Filling ${cleanedMetadata.registers.length - (row.length - 1)} missing values with null for row with timestamp ${rowTimestamp}`);
                    }

                    dataRows.push(data);
                }

                if (dataRows.length > 0) {
                    // Perform upsert operation for each row
                    for (const row of dataRows) {
                        await this.upsertDataRow(deviceKnex, row);
                    }
                    console.log(`Successfully upserted ${dataRows.length} rows of data for device ${mac}`);
                } else {
                    console.warn('No valid data rows to insert');
                }
            } else {
                // Legacy format (object with key-value pairs)
                console.log('Processing legacy format data for device', mac);

                const data = { timestamp: defaultTimestamp };

                // Map payload to columns
                for (const register of cleanedMetadata.registers) {
                    const key = register.index.toString();
                    const columnName = `channel_${register.index}`;

                    if (dataPayload[key] !== undefined) {
                        // Format value based on data type if needed
                        if (DtypeMapper && DtypeMapper.formatValueForSQL) {
                            data[columnName] = register.dtype
                                ? DtypeMapper.formatValueForSQL(dataPayload[key], register.dtype)
                                : dataPayload[key];
                        } else {
                            // Fallback if DtypeMapper is not available
                            data[columnName] = dataPayload[key];
                        }
                    } else {
                        data[columnName] = null;
                    }
                }

                // Log if we're dropping excess values
                if (Object.keys(dataPayload).length > cleanedMetadata.registers.length) {
                    console.warn(`Dropping ${Object.keys(dataPayload).length - cleanedMetadata.registers.length} excess values from legacy format data`);
                }

                // Log if we're filling missing values with null
                if (Object.keys(dataPayload).length < cleanedMetadata.registers.length) {
                    console.log(`Filling ${cleanedMetadata.registers.length - Object.keys(dataPayload).length} missing values with null for legacy format data`);
                }

                // Perform upsert for the single row
                await this.upsertDataRow(deviceKnex, data);
                console.log(`Data upserted successfully for device ${mac}`);
            }
        } catch (error) {
            console.error(`Error logging data for device ${mac}:`, error);
            throw error;
        }
    }

    async getLatestDataTimestamp(mac) {
        try {
            // ensure database connection
            let deviceKnex;
            try {
                deviceKnex = await this.getDeviceConnection(mac);
            } catch (connError) {
                console.log(`Device connection not found for ${mac} when getting timestamp, returning 0`);
                return 0;
            }

            // check if data table exists
            const hasTable = await deviceKnex.schema.hasTable('data');
            if (!hasTable) {
                console.log(`Data table doesn't exist for device ${mac}, returning 0`);
                return 0;
            }

            const result = await deviceKnex('data')
                .max('timestamp as latest')
                .first();

            console.log(`Latest timestamp for device ${mac}: ${result?.latest || 0}`);
            return result?.latest || 0;
        } catch (error) {
            console.error(`Error getting latest timestamp for device ${mac}:`, error);
            return 0;
        }
    }

    validateDataPayload(payload, metadata) {
        const errors = [];

        const cleanedMetadata = this.cleanMetadata(metadata);
        const registers = cleanedMetadata.registers;

        // map of register indices for easier lookup
        const registerMap = new Map();
        registers.forEach(register => {
            registerMap.set(register.index, register);
        });

        // Check if payload is in the new format (array of arrays)
        if (Array.isArray(payload) && payload.length > 0 && Array.isArray(payload[0])) {
            console.log(`Validating nested array format with ${payload.length} rows`);

            // Validate each row in the array
            payload.forEach((row, rowIndex) => {
                if (!Array.isArray(row)) {
                    errors.push(`Row ${rowIndex} is not an array`);
                    return;
                }

                if (row.length < 1) {
                    errors.push(`Row ${rowIndex} is empty`);
                    return;
                }

                // First element should be a timestamp
                const timestamp = row[0];
                if (isNaN(timestamp)) {
                    errors.push(`Invalid timestamp in row ${rowIndex}: ${timestamp}`);
                    return;
                }

                // Check if the row has fewer values than expected (timestamp + registers)
                // Row should have at least timestamp + 1 value to be valid
                if (row.length < 2) {
                    errors.push(`Row ${rowIndex} has no data values`);
                    return;
                }

                // Check if the number of data values exceeds the number of registers
                // Row length should be registers.length + 1 (for timestamp)
                if (row.length - 1 > registers.length) {
                    console.warn(`Row ${rowIndex} has more values (${row.length - 1}) than registers (${registers.length})`);
                    // This is just a warning, not an error, as excess values will be dropped
                }

                // Log if we're filling missing values with null
                if (row.length - 1 < registers.length) {
                    console.log(`Row ${rowIndex} has fewer values (${row.length - 1}) than registers (${registers.length}), missing values will be filled with null`);
                }

                // Validate each value in the row (skip the first element which is timestamp)
                // Only validate up to the number of registers we have defined or available values
                const maxIndex = Math.min(row.length - 1, registers.length);
                let rowHasInvalidValue = false;

                for (let i = 1; i <= maxIndex; i++) {
                    // Index in the register map is i-1 (since the first element in row is timestamp)
                    const dbIndex = i - 1;
                    const value = row[i];
                    const register = registerMap.get(dbIndex);

                    if (!register) {
                        console.warn(`No register definition found for index ${dbIndex} in row ${rowIndex}`);
                        continue;
                    }

                    const registerName = register.register || register.name || `register_${dbIndex}`;

                    if (!this.validateValue(value, register.dtype)) {
                        console.warn(`Invalid value for register ${registerName} in row ${rowIndex}: ${value}`);
                        rowHasInvalidValue = true;
                        errors.push(`Invalid value for register ${registerName} in row ${rowIndex}: ${value}`);
                    }
                }

                // Mark this specific row as invalid, but don't invalidate the entire payload
                if (rowHasInvalidValue) {
                    console.warn(`Row ${rowIndex} contains invalid values and will be skipped during processing`);
                }
            });
        } else {
            // Legacy format (object with key-value pairs)
            console.log('Validating legacy object format');

            // Check if the payload has more keys than registers
            if (Object.keys(payload).length > registers.length) {
                console.warn(`Payload has more values (${Object.keys(payload).length}) than registers (${registers.length})`);
                // This is just a warning, not an error, as excess values will be dropped
            }

            // Log if we're filling missing values with null
            if (Object.keys(payload).length < registers.length) {
                console.log(`Payload has fewer values (${Object.keys(payload).length}) than registers (${registers.length}), missing values will be filled with null`);
            }

            // Only validate keys that correspond to actual registers
            let payloadHasInvalidValue = false;

            Object.entries(payload).forEach(([index, value]) => {
                const i = parseInt(index, 10);

                // Skip indices that are beyond our register count
                if (i >= registers.length) {
                    console.warn(`Skipping validation for index ${index} as it exceeds register count`);
                    return;
                }

                const register = registerMap.get(i);

                if (!register) {
                    console.warn(`No register definition found for index ${index}`);
                    return;
                }

                const registerName = register.register || register.name || `register_${i}`;

                if (!this.validateValue(value, register.dtype)) {
                    console.warn(`Invalid value for register ${registerName}: ${value}`);
                    payloadHasInvalidValue = true;
                    errors.push(`Invalid value for register ${registerName}: ${value}`);
                }
            });

            // Mark this payload as having invalid values, but don't invalidate the entire request
            if (payloadHasInvalidValue) {
                console.warn(`Legacy format payload contains invalid values and will be skipped during processing`);
            }
        }

        return { valid: errors.length === 0, errors };
    }

    validateValue(value, dtype) {
        if (!dtype) {
            console.log(`Null or undefined dtype for value: ${value}, allowing any value`);
            return true;
        }

        try {
            // normalize for consistent comparison
            const normalizedType = dtype.toUpperCase();

            switch (normalizedType) {
                case 'INT':
                case 'I32':
                case 'I16':
                case 'I8':
                    return Number.isInteger(value) || value === null;
                case 'FLOAT':
                case 'F32':
                case 'F64':
                    return !isNaN(parseFloat(value)) || value === null;
                case 'STR':
                case 'STRING':
                    return typeof value === 'string' || value === null;
                case 'B':
                case 'BOOL':
                case 'BOOLEAN':
                    return typeof value === 'boolean' || value === 1 || value === 0 ||
                        value === '1' || value === '0' ||
                        value === 'true' || value === 'false' ||
                        value === null;
                default:
                    // unknown dtypes : permissive
                    console.log(`Unknown data type: ${dtype}, allowing value: ${value}`);
                    return true;
            }
        } catch (error) {
            console.error(`Error validating value ${value} with dtype ${dtype}:`, error);
            return true;
        }
    }

    parseDataPayload(rawPayload, format = 'JSON') {
        try {
            switch (format) {
                case 'JSON':
                    // Parse if string, otherwise use as is
                    const jsonData = typeof rawPayload === 'object' ? rawPayload : JSON.parse(rawPayload);

                    // Check if it's the new format (array of arrays)
                    if (Array.isArray(jsonData) && jsonData.length > 0 && Array.isArray(jsonData[0])) {
                        console.log('Detected nested array format for JSON payload');
                        // Return the parsed nested array directly
                        return jsonData;
                    } else {
                        // Legacy format (object with key-value pairs)
                        console.log('Detected legacy object format for JSON payload');
                        return jsonData;
                    }

                case 'CSV':
                    // Split by newlines for multiple rows
                    const rows = rawPayload.trim().split('\n');
                    console.log(`CSV payload contains ${rows.length} rows`);

                    // Parse each row into an array of values
                    return rows.map(row => {
                        const values = row.split(',').map(val =>
                            val.trim() === '' ? null : isNaN(val) ? val : parseFloat(val)
                        );
                        return values;
                    });

                default:
                    throw new Error(`Unsupported payload format: ${format}`);
            }
        } catch (error) {
            console.error(`Error parsing data payload:`, error);
            throw new Error(`Failed to parse data payload: ${error.message}`);
        }
    }

    normalizeValue(value, dtype) {
        if (value === null || value === undefined) return null;

        if (!dtype) {
            console.log(`Null or undefined dtype for value: ${value}, preserving original value type`);
            return value;
        }

        try {
            // normalized for consistency
            const normalizedType = dtype.toLowerCase();

            switch (normalizedType) {
                case 'float':
                case 'f32':
                case 'f64':
                    return parseFloat(value);
                case 'int':
                case 'i32':
                case 'i16':
                case 'i8':
                    return parseInt(value);
                case 'str':
                case 'string':
                    return String(value);
                case 'b':
                case 'bool':
                case 'boolean':
                    if (typeof value === 'string') {
                        return value.toLowerCase() === 'true' || value === '1';
                    }
                    return Boolean(value);
                default:
                    console.log(`Unknown data type for normalization: ${dtype}, preserving original value type`);
                    return value;
            }
        } catch (error) {
            console.error(`Error normalizing value ${value} with dtype ${dtype}:`, error);
            return value;
        }
    }

    /**
     * migrate the data table based on metadata changes
     * @param {string} mac - Device MAC address
     * @param {object} currentMetadata - Current device metadata
     * @param {object} newMetadata - New device metadata
     * @param {object} changes - Metadata comparison results
     */
    async migrateDataTable(mac, currentMetadata, newMetadata, changes) {
        console.log(`Starting data migration for device ${mac} with strategy: ${changes.migrationStrategy}`);

        const cleanedCurrentMetadata = this.cleanMetadata(currentMetadata);
        const cleanedNewMetadata = this.cleanMetadata(newMetadata);

        let deviceKnex = await this.getDeviceConnection(mac);
        if (!deviceKnex) {
            throw new Error(`Could not establish database connection for device ${mac}`);
        }

        try {
            switch (changes.migrationStrategy) {
                case 'ADD_COLUMNS':
                    // clean added registers
                    const cleanedAddedRegisters = changes.addedRegisters.map(register => {
                        if (!register || typeof register !== 'object' || !register.dtype) {
                            console.log(`Skipping invalid register in addedRegisters`);
                            return null;
                        }
                        return register;
                    }).filter(Boolean);

                    await this.addColumnsToTable(deviceKnex, cleanedAddedRegisters);
                    break;
                case 'DROP_COLUMNS':
                    // clean removed registers
                    const cleanedRemovedRegisters = changes.removedRegisters.map(register => {
                        if (!register || typeof register !== 'object') {
                            console.log(`Skipping invalid register in removedRegisters`);
                            return null;
                        }
                        return register;
                    }).filter(Boolean);

                    await this.dropColumnsFromTable(deviceKnex, cleanedRemovedRegisters);
                    break;
                case 'RECREATE_TABLE':
                    // logging : added and removed registers
                    const addedRegisters = changes.addedRegisters || [];
                    const removedRegisters = changes.removedRegisters || [];

                    console.log(`Recreating table with ${changes.changedDtypeRegisters.length} dtype changes, ` +
                                `${addedRegisters.length} added columns, and ${removedRegisters.length} removed columns`);

                    await this.recreateTable(deviceKnex, cleanedNewMetadata, false);
                    break;
                case 'RECREATE_TABLE_PRESERVE_DATA':
                    // logging : added and removed registers
                    const addedRegs = changes.addedRegisters || [];
                    const removedRegs = changes.removedRegisters || [];
                    const changedDtypes = changes.changedDtypeRegisters || [];

                    console.log(`Recreating table with data preservation: ` +
                                `${changedDtypes.length} dtype changes, ` +
                                `${addedRegs.length} added columns, and ` +
                                `${removedRegs.length} removed columns`);

                    await this.recreateTable(deviceKnex, cleanedNewMetadata, true, cleanedCurrentMetadata);
                    break;
                default:
                    console.log(`No migration needed or unknown strategy: ${changes.migrationStrategy}`);
            }
            console.log(`Data migration completed for device ${mac}`);
        } catch (error) {
            console.error(`Error during data migration for device ${mac}:`, error);
            throw error;
        }
    }

    /**
     * get or create a connection to the device database
     * @param {string} mac - Device MAC address
     * @returns {object} Knex connection
     */
    async getDeviceConnection(mac) {

        const normalizedMac = mac.toLowerCase();

        // check if connection already exists
        let deviceKnex = this.deviceConnections?.[normalizedMac];

        if (!deviceKnex) {
            console.log(`Creating new database connection for device ${normalizedMac}`);

            // MAC for filename : replace `:` with `_` and lowercase
            const cleanMac = normalizedMac.replace(/:/g, '_');
            const deviceDbPath = join(this.dataDir, `${cleanMac}.db`);

            console.log(`Database file path: ${deviceDbPath}`);

            // ensure the data directory exists
            if (!fs.existsSync(this.dataDir)) {
                console.log(`Creating data directory: ${this.dataDir}`);
                fs.mkdirSync(this.dataDir, { recursive: true });
            }

            try {
                const { default: Knex } = await import('knex');

                // device-specific connection
                deviceKnex = Knex({
                    client: 'sqlite3',
                    connection: {
                        filename: deviceDbPath
                    },
                    useNullAsDefault: true,
                    pool: {
                        afterCreate: (conn, cb) => {
                            conn.run('PRAGMA foreign_keys = ON', cb);
                        }
                    }
                });

                // connection test
                await deviceKnex.raw('SELECT 1');
                console.log(`Successfully connected to database for device ${normalizedMac}`);

                // store connection
                this.deviceConnections = this.deviceConnections || {};
                this.deviceConnections[normalizedMac] = deviceKnex;
            } catch (error) {
                console.error(`Error creating database connection for device ${normalizedMac}:`, error);
                throw new Error(`Failed to create database connection: ${error.message}`);
            }
        }

        return deviceKnex;
    }

    /**
     * add columns to the data table
     * @param {object} deviceKnex - Knex connection
     * @param {array} addedRegisters - Array of added registers
     */
    async addColumnsToTable(deviceKnex, addedRegisters) {
        console.log(`Adding ${addedRegisters.length} columns to data table`);

        try {
            const tableInfo = await deviceKnex.raw('PRAGMA table_info(data)');
            const existingColumns = new Set(tableInfo.map(col => col.name));

            console.log(`Existing columns in table: ${Array.from(existingColumns).join(', ')}`);

            const newRegisters = addedRegisters.filter(signal => {
                if (!signal || typeof signal !== 'object') {
                    console.log(`Skipping null or non-object register`);
                    return false;
                }

                const columnName = `channel_${signal.index}`;
                if (existingColumns.has(columnName)) {
                    console.log(`Column ${columnName} already exists, skipping to avoid duplicate column error`);
                    return false;
                }

                return true;
            });

            if (newRegisters.length === 0) {
                console.log('No new columns to add after filtering out existing columns');
                return;
            }

            console.log(`Adding ${newRegisters.length} new columns to data table`);

            await deviceKnex.schema.table('data', table => {
                newRegisters.forEach(signal => {
                    const columnName = `channel_${signal.index}`;

                    if (signal.isEmptyRegister || signal.dtype === null) {
                        console.log(`Adding column ${columnName} for empty register with null dtype`);
                        table.text(columnName);
                        return;
                    }

                    console.log(`Adding column ${columnName} with type ${signal.dtype}`);

                    try {
                        // normalize dtype to lowercase for consistent comparison
                        const dtype = signal.dtype.toLowerCase();

                        switch (dtype) {
                            case 'float':
                            case 'f32':
                            case 'f64':
                                table.float(columnName);
                                break;
                            case 'int':
                            case 'i32':
                            case 'i16':
                            case 'i8':
                                table.integer(columnName);
                                break;
                            case 'str':
                            case 'string':
                                table.string(columnName);
                                break;
                            case 'b':
                            case 'bool':
                            case 'boolean':
                                table.boolean(columnName);
                                break;
                            default:
                                console.log(`Unknown data type: ${signal.dtype}, defaulting to text for column ${columnName}`);
                                table.text(columnName);
                        }
                    } catch (error) {
                        console.error(`Error adding column ${columnName}:`, error);
                        // create a default string column on error
                        table.string(columnName);
                    }
                });
            });

            console.log('Columns added successfully');
        } catch (error) {
            console.error(`Error in addColumnsToTable: ${error.message}`);
            throw error;
        }
    }

    /**
     * drop columns from the data table
     * @param {object} deviceKnex - Knex connection
     * @param {array} removedRegisters - Array of removed registers
     */
    async dropColumnsFromTable(deviceKnex, removedRegisters) {
        console.log(`Dropping ${removedRegisters.length} columns from data table`);

        await deviceKnex.schema.table('data', table => {
            removedRegisters.forEach(signal => {
                const columnName = `channel_${signal.index}`;
                console.log(`Dropping column ${columnName}`);
                table.dropColumn(columnName);
            });
        });

        console.log('Columns dropped successfully');
    }

    /**
     * recreate the data table with new schema
     * @param {object} deviceKnex - Knex connection
     * @param {object} newMetadata - New device metadata
     * @param {boolean} preserveData - Whether to preserve existing data
     * @param {object} currentMetadata - Current device metadata (needed if preserveData is true)
     */
    async recreateTable(deviceKnex, newMetadata, preserveData = false, currentMetadata = null) {
        console.log(`Recreating data table with preserveData=${preserveData}`);

        const tableName = 'data';
        const tempTableName = 'data_temp';

        // preserving data : create a temporary table with old schema and copy data
        if (preserveData && currentMetadata) {
            console.log('Creating temporary table to preserve data');

            // column mapping between old and new schema
            const currentRegs = currentMetadata.registers || [];
            const newRegs = newMetadata.registers || [];

            // map new registers by register `register` or `name`   // name deprecated
            const newRegMap = new Map();

            // first pass: add all registers with valid identifiers
            newRegs.forEach((reg, idx) => {
                const regWithIndex = typeof reg === 'object' && reg !== null ? { ...reg, index: idx } : { index: idx };
                const regId = reg && (reg.register || reg.name);

                if (regId) {
                    console.log(`Adding register mapping: ${regId} -> index ${idx}`);
                    newRegMap.set(regId, regWithIndex);
                }
            });

            // second pass: add empty registers
            newRegs.forEach((reg, idx) => {
                const regId = reg && (reg.register || reg.name);
                if (regId && newRegMap.has(regId)) {
                    return;
                }

                // handle empty registers
                if (!reg || (typeof reg === 'object' && Object.keys(reg).length === 0) || reg.isEmptyRegister) {
                    const emptyKey = `__empty_${idx}__`;
                    console.log(`Adding empty register mapping: ${emptyKey} -> index ${idx}`);
                    newRegMap.set(emptyKey, {
                        ...((typeof reg === 'object' && reg !== null) ? reg : {}),
                        index: idx,
                        isEmptyRegister: true
                    });
                }
            });

            const commonSignals = [];

            // map of current registers by index
            const currentRegsByIndex = new Map();
            currentRegs.forEach((reg, idx) => {
                if (reg && typeof reg === 'object') {
                    currentRegsByIndex.set(idx, { ...reg, index: idx });
                }
            });

            const matchedNewRegisters = new Map();

            // process registers in current metadata
            currentRegs.forEach((reg, idx) => {
                if (!reg || typeof reg !== 'object') {
                    console.log(`Skipping invalid register at index ${idx}`);
                    return;
                }

                // ensure index property
                const regWithIndex = { ...reg, index: idx };

                let hasMatch = false;
                let matchKey = null;
                let matchedNewIndex = null;

                const regId = reg.register || reg.name;
                if (regId && newRegMap.has(regId)) {
                    const mapping = newRegMap.get(regId);

                    if (matchedNewRegisters.has(mapping.index)) {
                        console.warn(`New register at index ${mapping.index} (${regId}) has already been matched, skipping to avoid duplicate mapping`);
                    } else {
                        hasMatch = true;
                        matchKey = regId;
                        matchedNewIndex = mapping.index;
                        console.log(`Found matching register: ${regId} (current index ${idx} -> new index ${mapping.index})`);
                    }
                }
                else if (reg.isEmptyRegister || Object.keys(reg).length === 0) {
                    const emptyKey = `__empty_${idx}__`;
                    if (newRegMap.has(emptyKey)) {
                        const mapping = newRegMap.get(emptyKey);

                        if (matchedNewRegisters.has(mapping.index)) {
                            console.warn(`New empty register at index ${mapping.index} has already been matched, skipping to avoid duplicate mapping`);
                        } else {
                            hasMatch = true;
                            matchKey = emptyKey;
                            matchedNewIndex = mapping.index;
                            console.log(`Found matching empty register at index ${idx} -> new index ${mapping.index}`);
                        }
                    }
                }

                if (hasMatch && matchedNewIndex !== null) {
                    matchedNewRegisters.set(matchedNewIndex, idx);

                    commonSignals.push({
                        ...regWithIndex,
                        matchKey: matchKey,
                        newIndex: matchedNewIndex
                    });
                }
            });

            console.log(`Found ${commonSignals.length} common signals between current and new metadata`);

            // Debug: log the mapping
            console.log('Register mapping:');
            commonSignals.forEach(signal => {
                try {
                    const signalId = signal.register || signal.name || 'empty';

                    if (signal.newIndex !== undefined) {
                        console.log(`  ${signal.index} (${signalId}) -> ${signal.newIndex}`);
                    } else if (signal.matchKey) {
                        const mapping = newRegMap.get(signal.matchKey);
                        if (mapping) {
                            console.log(`  ${signal.index} (${signalId}) -> ${mapping.index}`);
                        } else {
                            console.warn(`  No mapping found for signal at index ${signal.index} with matchKey ${signal.matchKey}`);
                        }
                    } else {
                        console.warn(`  Signal at index ${signal.index} has no matchKey or newIndex`);
                    }
                } catch (error) {
                    console.error(`Error logging mapping for signal at index ${signal.index}:`, error);
                }
            });

            if (commonSignals.length > 0) {
                // temp table
                await deviceKnex.schema.createTable(tempTableName, table => {
                    table.integer('timestamp').notNullable();

                    // to avoid duplicates
                    const addedColumns = new Set();

                    // Only columns that exist in both schemas
                    commonSignals.forEach(signal => {
                        const oldColumnName = `channel_${signal.index}`;

                        let newIndex;
                        let newColumnName;

                        try {
                            // newIndex : from common signals filtering
                            if (signal.newIndex !== undefined) {
                                newIndex = signal.newIndex;
                                newColumnName = `channel_${newIndex}`;
                            } else {
                                if (!signal.matchKey) {
                                    console.warn(`Signal at index ${signal.index} has no matchKey or newIndex, skipping`);
                                    return;
                                }

                                const mapping = newRegMap.get(signal.matchKey);
                                if (!mapping) {
                                    console.warn(`No mapping found for signal at index ${signal.index} with matchKey ${signal.matchKey}, skipping`);
                                    return;
                                }

                                newIndex = mapping.index;
                                newColumnName = `channel_${newIndex}`;
                            }

                            // check if already added
                            if (addedColumns.has(newColumnName)) {
                                console.warn(`Column ${newColumnName} has already been added, skipping to avoid duplicate column error`);
                                return;
                            }

                            // mark as added
                            addedColumns.add(newColumnName);
                        } catch (error) {
                            console.error(`Error getting mapping for signal at index ${signal.index}:`, error);
                            return;
                        }

                        console.log(`Mapping column ${oldColumnName} to ${newColumnName}`);

                        // empty registers (null dtype)
                        if (!signal.dtype) {
                            console.log(`Creating column ${newColumnName} for empty register with null dtype`);
                            // empty registers : text column
                            table.text(newColumnName);
                            return;
                        }

                        try {
                            // normalize for consistent comparison
                            const dtype = signal.dtype.toLowerCase();

                            switch (dtype) {
                                case 'float':
                                case 'f32':
                                case 'f64':
                                    table.float(newColumnName);
                                    break;
                                case 'int':
                                case 'i32':
                                case 'i16':
                                case 'i8':
                                    table.integer(newColumnName);
                                    break;
                                case 'str':
                                case 'string':
                                    table.string(newColumnName);
                                    break;
                                case 'b':
                                case 'bool':
                                case 'boolean':
                                    table.boolean(newColumnName);
                                    break;
                                default:
                                    console.log(`Unknown data type: ${signal.dtype}, defaulting to text for column ${newColumnName}`);
                                    table.text(newColumnName);
                            }
                        } catch (error) {
                            console.error(`Error creating column ${newColumnName}:`, error);
                            table.text(newColumnName);
                        }
                    });
                });

                // copy data from old table to temporary table
                // filtered list of signals that have valid mappings
                const validMappedSignals = [];
                const columnMappings = [];
                const usedNewColumns = new Set(); // used new column names to avoid duplicates

                commonSignals.forEach(signal => {
                    const oldColumnName = `channel_${signal.index}`;
                    let newColumnName;

                    try {
                        // newIndex property that was added during the common signals filtering
                        if (signal.newIndex !== undefined) {
                            newColumnName = `channel_${signal.newIndex}`;
                        } else {
                            if (!signal.matchKey) {
                                console.warn(`Signal at index ${signal.index} has no matchKey or newIndex, skipping from data copy`);
                                return;
                            }

                            const mapping = newRegMap.get(signal.matchKey);
                            if (!mapping) {
                                console.warn(`No mapping found for signal at index ${signal.index} with matchKey ${signal.matchKey}, skipping from data copy`);
                                return;
                            }

                            newColumnName = `channel_${mapping.index}`;
                        }

                        // if new column name is already used
                        if (usedNewColumns.has(newColumnName)) {
                            console.warn(`Duplicate column mapping detected: ${oldColumnName} -> ${newColumnName}, skipping to avoid SQL error`);
                            return;
                        }

                        // valid signals and column mappings
                        validMappedSignals.push(signal);
                        columnMappings.push({
                            oldColumn: oldColumnName,
                            newColumn: newColumnName
                        });

                        usedNewColumns.add(newColumnName);

                        console.log(`Adding column mapping: ${oldColumnName} -> ${newColumnName}`);
                    } catch (error) {
                        console.error(`Error creating mapping for signal at index ${signal.index}:`, error);
                    }
                });

                // valid mappings check
                if (validMappedSignals.length === 0) {
                    console.warn('No valid column mappings found, skipping data preservation');
                    preserveData = false;
                    return;
                }

                const oldColumns = ['timestamp', ...validMappedSignals.map(signal => `channel_${signal.index}`)];
                const newColumns = ['timestamp', ...columnMappings.map(mapping => mapping.newColumn)];

                // insert with column mapping
                const insertQuery = deviceKnex.select(oldColumns)
                    .from(tableName)
                    .toString();

                let mappedQuery = insertQuery;

                // first column<timestamp>, rest : replace
                for (let i = 1; i < oldColumns.length && i < newColumns.length; i++) {
                    console.log(`Replacing ${oldColumns[i]} with ${newColumns[i]} in query`);
                    const regex = new RegExp(`\\b${oldColumns[i]}\\b`, 'g');
                    mappedQuery = mappedQuery.replace(regex, newColumns[i]);
                }

                // duplicate columns
                const columnSet = new Set();
                let hasDuplicates = false;
                let duplicateColumns = [];

                newColumns.forEach(col => {
                    if (columnSet.has(col)) {
                        hasDuplicates = true;
                        duplicateColumns.push(col);
                    }
                    columnSet.add(col);
                });

                if (hasDuplicates) {
                    throw new Error(`Duplicate columns detected in SQL statement: ${duplicateColumns.join(', ')}. This should not happen with the duplicate prevention logic.`);
                }

                // execute insert
                try {
                    const sqlStatement = `INSERT INTO ${tempTableName} (${newColumns.join(', ')}) ${mappedQuery.substring(mappedQuery.indexOf('SELECT'))}`;
                    console.log('SQL statement for copying data to temporary table:');
                    console.log(sqlStatement);

                    const tempTableInfo = await deviceKnex.raw(`PRAGMA table_info(${tempTableName})`);
                    console.log(`Temporary table has ${tempTableInfo.length} columns`);

                    // execute insert
                    await deviceKnex.raw(sqlStatement);

                    // verify data copied
                    const rowCount = await deviceKnex(tempTableName).count('* as count').first();
                    console.log(`Data copied to temporary table: ${rowCount.count} rows`);
                } catch (error) {
                    console.error('Error executing SQL insert:', error);
                    throw new Error(`Failed to copy data to temporary table: ${error.message}`);
                }
            } else {
                console.log('No common columns found, skipping data preservation');
                preserveData = false;
            }
        }

        // drop  original table
        await deviceKnex.schema.dropTableIfExists(tableName);
        console.log('Original table dropped');

        // create table with updated schema
        console.log(`Creating new table with ${newMetadata.registers.length} columns (plus timestamp)`);

        // track created columns
        const createdColumns = ['timestamp'];

        await deviceKnex.schema.createTable(tableName, table => {
            table.integer('timestamp').notNullable();

            // create columns based on new metadata
            newMetadata.registers.forEach((register, index) => {
                const columnName = `channel_${index}`;
                createdColumns.push(columnName);

                // empty registers : null dtype
                if (!register.dtype) {
                    console.log(`Creating column ${columnName} for empty register with null dtype`);
                    // empty registers : text column
                    table.text(columnName);
                    return;
                }

                console.log(`Creating column ${columnName} with type ${register.dtype}`);

                try {
                    // normalize for consistent comparison
                    const dtype = register.dtype.toLowerCase();

                    switch (dtype) {
                        case 'float':
                        case 'f32':
                        case 'f64':
                            table.float(columnName);
                            break;
                        case 'int':
                        case 'i32':
                        case 'i16':
                        case 'i8':
                            table.integer(columnName);
                            break;
                        case 'str':
                        case 'string':
                            table.string(columnName);
                            break;
                        case 'b':
                        case 'bool':
                        case 'boolean':
                            table.boolean(columnName);
                            break;
                        default:
                            console.log(`Unknown data type: ${register.dtype}, defaulting to text for column ${columnName}`);
                            table.text(columnName);
                    }
                } catch (error) {
                    console.error(`Error creating column ${columnName}:`, error);
                    // create default text column on error
                    table.text(columnName);
                }
            });

            table.index(['timestamp']);
        });

        // verify actual columns in created table
        const tableInfo = await deviceKnex.raw(`PRAGMA table_info(${tableName})`);
        const actualColumns = tableInfo.map(col => col.name);

        console.log(`New table created with ${actualColumns.length} columns:`, actualColumns);

        // if all expected columns were created
        if (actualColumns.length !== createdColumns.length) {
            console.warn(`Column count mismatch: expected ${createdColumns.length}, got ${actualColumns.length}`);

            // missing columns
            const missingColumns = createdColumns.filter(col => !actualColumns.includes(col));
            if (missingColumns.length > 0) {
                console.warn(`Missing columns: ${missingColumns.join(', ')}`);
            }

            // extra columns
            const extraColumns = actualColumns.filter(col => !createdColumns.includes(col));
            if (extraColumns.length > 0) {
                console.warn(`Extra columns: ${extraColumns.join(', ')}`);
            }
        }

        // if preserving data, copy from temporary table to new table
        if (preserveData) {
            try {
                const tempTableInfo = await deviceKnex.raw(`PRAGMA table_info(${tempTableName})`);
                const dataTableInfo = await deviceKnex.raw(`PRAGMA table_info(${tableName})`);

                const tempColumns = tempTableInfo.map(col => col.name);
                const dataColumns = dataTableInfo.map(col => col.name);

                console.log(`Temporary table columns (${tempColumns.length}):`, tempColumns);
                console.log(`New data table columns (${dataColumns.length}):`, dataColumns);

                // common columns between tables
                const commonColumns = tempColumns.filter(col => dataColumns.includes(col));
                console.log(`Common columns (${commonColumns.length}):`, commonColumns);

                if (commonColumns.length === 0) {
                    console.warn('No common columns found between temporary and new table, skipping data copy');
                } else {
                    // insert : explicitly specifies columns
                    const insertQuery = `
                        INSERT INTO ${tableName} (${commonColumns.join(', ')})
                        SELECT ${commonColumns.join(', ')} FROM ${tempTableName}
                    `;

                    console.log(`Executing insert query with explicit columns: ${insertQuery}`);
                    await deviceKnex.raw(insertQuery);
                    console.log('Data copied to new table with explicit column mapping');
                }

                await deviceKnex.schema.dropTable(tempTableName);
                console.log('Temporary table dropped');
            } catch (error) {
                console.error('Error copying data from temporary table to new table:', error);
                // drop the temporary table even if the copy failed
                try {
                    await deviceKnex.schema.dropTable(tempTableName);
                    console.log('Temporary table dropped after error');
                } catch (dropError) {
                    console.error('Error dropping temporary table:', dropError);
                }
                throw new Error(`Failed to copy data from temporary table to new table: ${error.message}`);
            }
        }

        console.log('Table recreation completed');
    }

    /**
     * Log register details for debugging
     * @param {array} registers - Array of register objects
     */
    logRegisterDetails(registers) {
        if (!registers || !registers.length) {
            console.log('No registers to log');
            return;
        }

        console.log('Register details:');
        registers.forEach((reg, index) => {
            console.log(`  [${index}] ${reg.register || reg.name || 'unnamed'}: {`);
            console.log(`    dtype: ${reg.dtype || 'undefined'}`);
            console.log(`    group: ${reg.group || 'undefined'}`);
            console.log(`    device: ${reg.device || 'undefined'}`);
            console.log(`    units: ${reg.units || 'undefined'}`);
            console.log(`    display: ${reg.display || 'undefined'}`);
            console.log('  }');
        });
    }

    // Helper method to perform upsert operation
    async upsertDataRow(deviceKnex, dataRow) {
        try {
            const timestamp = dataRow.timestamp;

            // Check if a record with this timestamp already exists
            const existingRecord = await deviceKnex('data')
                .where({ timestamp })
                .first();

            if (existingRecord) {
                // Update existing record
                console.log(`Updating existing record with timestamp ${timestamp}`);
                await deviceKnex('data')
                    .where({ timestamp })
                    .update(dataRow);
            } else {
                // Insert new record
                console.log(`Inserting new record with timestamp ${timestamp}`);
                await deviceKnex('data').insert(dataRow);
            }
        } catch (error) {
            console.error(`Error performing upsert operation:`, error);
            throw error;
        }
    }

    /**
     * Query device data from the device-specific database
     * @param {string} mac - Device MAC address
     * @param {object} options - Query options
     * @param {number} options.from - Start timestamp
     * @param {number} options.to - End timestamp
     * @param {string[]} options.fields - Specific fields to retrieve (null for all)
     * @param {number} options.limit - Maximum number of records to return
     * @returns {Promise<Array>} Array of data records
     */
    async queryDeviceData(mac, options) {
        try {
            const { from, to, fields = null, limit = 1000 } = options;
            
            console.log(`Querying data for device ${mac} from ${from} to ${to}`);
            
            // Get device metadata to understand the data structure
            const device = await this.deviceService.findDeviceByMac(mac);
            if (!device) {
                console.warn(`Device not found: ${mac}`);
                return [];
            }
            
            // Get database connection
            let deviceKnex;
            try {
                deviceKnex = await this.getDeviceConnection(mac);
            } catch (connError) {
                console.error(`Failed to connect to database for device ${mac}:`, connError);
                return [];
            }
            
            // Check if data table exists
            const hasTable = await deviceKnex.schema.hasTable('data');
            if (!hasTable) {
                console.warn(`Data table doesn't exist for device ${mac}`);
                return [];
            }            // Start building the query
            let query = deviceKnex('data')
                .orderBy('timestamp', 'asc');
            
            // Apply timestamp filters only if provided and valid
            if (from !== null && from !== undefined && !isNaN(from)) {
                query = query.where('timestamp', '>=', from);
            }
            if (to !== null && to !== undefined && !isNaN(to)) {
                query = query.where('timestamp', '<=', to);
            }
            
            // Apply limit
            if (limit) {
                query = query.limit(limit);
            }
            
            // Get metadata to map channel columns to register names
            let metadata;
            try {
                metadata = typeof device.meta === 'string' ? JSON.parse(device.meta) : device.meta;
            } catch (e) {
                console.error(`Error parsing device metadata: ${e.message}`);
                metadata = { registers: [] };
            }
            
            // Clean metadata
            const cleanedMetadata = this.cleanMetadata ? 
                this.cleanMetadata(metadata) : 
                (this.deviceService.cleanMetadata ? 
                    this.deviceService.cleanMetadata(metadata) : 
                    metadata);
            
            // Get register information for field mapping
            const registers = cleanedMetadata.registers || [];
            const registerMap = new Map();
            
            // Build a map of channel names to register info
            registers.forEach((register, index) => {
                const columnName = `channel_${index}`;
                registerMap.set(columnName, {
                    name: register.name || register.register || `register_${index}`,
                    dtype: register.dtype,
                    index: index
                });
            });
              // Select specific fields if provided
            if (fields && fields.length > 0) {
                // Always include timestamp
                const selectedColumns = ['timestamp'];
                
                // Find the channel columns that match the requested fields
                registers.forEach((register, index) => {
                    const registerName = register.name || register.register;
                    if (registerName && fields.includes(registerName)) {
                        selectedColumns.push(`channel_${index}`);
                    }
                });
                
                // Apply the field selection to the query
                query = query.select(selectedColumns);
            }
            
            console.log(`Executing query: ${query.toString()}`);
            
            // Execute the query
            const rawData = await query;
            console.log(`Retrieved ${rawData.length} records for device ${mac}`);
            
            if (rawData.length > 0) {
                console.log(`Sample raw data (first record):`, rawData[0]);
            }
            
            // Transform the data to use register names instead of channel_X
            const transformedData = rawData.map(row => {
                const result = { timestamp: row.timestamp };
                
                // Process each column in the row
                Object.keys(row).forEach(key => {
                    if (key === 'timestamp') return;
                    
                    // Get the register info for this channel
                    const registerInfo = registerMap.get(key);
                    if (registerInfo) {
                        // Use the register name as the key
                        result[registerInfo.name] = row[key];
                    } else {
                        // Fallback to using the original column name
                        result[key] = row[key];
                    }
                });
                
                return result;
            });
            
            return transformedData;
        } catch (error) {
            console.error(`Error querying data for device ${mac}:`, error);
            return [];
        }
    }
}
