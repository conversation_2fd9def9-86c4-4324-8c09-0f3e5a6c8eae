import fs from 'fs/promises'
import dataPoster from './data_poster/data_poster.js';
import { generateLiveData } from './live_data_generator.js';
import { startAlignedInterval } from './alignedInterval.js'

const { init, svc, shutdown } = dataPoster

// testing config 
const projectSettings = {
    fine: 'C:/Users/<USER>/OneDrive/Desktop/novibe/data',
    metadata: 'C:/Users/<USER>/OneDrive/Desktop/novibe/data/metadata.json',
    // serverUrl: 'https://httpbin.org/delay/2'  // 2 second delay to test sequential processing
    serverUrl: "http://127.0.0.1:9001/api/device/data"
}

const SVC_INTERVAL_MS = 2000
const DATA_GEN_INTERVAL_MS = 2000

let stopSvcInterval = null
let stopDataGenInterval = null
let metadata = null

async function main() {
    console.log("Initializing data poster...");

    try {
        const metaContent = await fs.readFile(projectSettings.metadata, 'utf8')
        metadata = JSON.parse(metaContent)
    } catch (error) {
        console.error("Failed to load metadata:", error)
        process.exit(1)
    }

    init(projectSettings)



    console.log("Starting data generation...")
    stopDataGenInterval = startAlignedInterval(() => {
        try {
            const livePoint = generateLiveData(metadata)
            if (livePoint) {
                svc(livePoint)
            }
        } catch (e) {
            console.error("Error in data generation:", e)
        }
    }, DATA_GEN_INTERVAL_MS)

    // handlers for shutdown signals
    setupSignalHandlers();
    console.log("Application running");
}

function setupSignalHandlers() {
    // Handle Ctrl+C
    process.on('SIGINT', async () => {
        console.log("\nReceived shutdown signal (Ctrl+C)");
        await gracefulShutdown();
    });

    // Handle other termination signals
    process.on('SIGTERM', async () => {
        console.log("\nReceived termination signal");
        await gracefulShutdown();
    });
}

async function gracefulShutdown() {
    console.log("Shutting down gracefully...");

    if (stopSvcInterval) {
        stopSvcInterval();
        stopSvcInterval = null;
        console.log("Service interval stopped");
    }
    if (stopDataGenInterval) {
        stopDataGenInterval()
        stopDataGenInterval = null
        console.log("Data generation stopped")
    }

    await shutdown();

    console.log("Shutdown complete");
    process.exit(0);
}

main().catch(async error => {
    console.error("Fatal application error:", error);
    await gracefulShutdown();
    process.exit(1);
});