function startAlignedInterval(callback, interval) {
    let timeoutId

    const scheduleNextExecution = () => {
        const now = Date.now()
        const delay = interval - (now % interval)

        timeoutId = setTimeout(() => {
            callback()
            scheduleNextExecution();
        }, delay);
    }

    scheduleNextExecution()

    return () => clearTimeout(timeoutId)

}
export { startAlignedInterval }