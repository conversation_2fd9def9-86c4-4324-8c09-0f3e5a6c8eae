import Fastify from 'fastify';
import knex from 'knex';
import knexconfig from '../knexfile.js';
import authPlugin from './plugins/authPlugin.js';
import corsPlugin from './plugins/corsPlugin.js';
import deviceRoutes from './routes/device/deviceRoutes.js';
import db from './database/db.js';
import dbPlugin from './plugins/db.js';
import logger from './utils/logger.js';

import { config } from 'dotenv';

config();

const env = process.env.NODE_ENV || 'development';
const knexInstance = knex(knexconfig[env]);

const fastifyDevice = Fastify({
  logger: false, // Disable Fastify's built-in logger
  disableRequestLogging: true, // Disable automatic request logging
});

const addCSVParser = (fastify) => {
  fastify.addContentTypeParser('text/csv', { parseAs: 'string' }, (req, body, done) => {
    try {
      done(null, body);
    } catch (err) {
      done(err, undefined);
    }
  });
};

addCSVParser(fastifyDevice);

const registerPlugins = async (fastify) => {
  await fastify.register(authPlugin);
  await fastify.register(corsPlugin);
  await fastify.register(dbPlugin);
  fastify.decorate('db', db);
};

await registerPlugins(fastifyDevice);

await fastifyDevice.register(deviceRoutes, { prefix: '/api/device' });

logger.info(`Device server Environment: ${env}`);
logger.info('Device API logger initialized successfully');

// Add a hook to log requests using our Winston logger
fastifyDevice.addHook('onRequest', (request, reply, done) => {
  const { method, url } = request;
  const mac = request.headers['mac'] || 'unknown';
  const deviceTime = request.headers['dev-time'] || 'unknown';
  
  logger.http(`${method} ${url}`, { 
    mac, 
    deviceTime,
    contentType: request.headers['content-type']
  });
  
  done();
});

// Add a hook to log responses
fastifyDevice.addHook('onResponse', (request, reply, done) => {
  const responseTime = reply.getResponseTime();
  const statusCode = reply.statusCode;
  
  // Only log detailed info for non-200 responses
  if (statusCode >= 400) {
    logger.warn(`Response: ${statusCode} (${responseTime.toFixed(2)}ms)`, {
      mac: request.headers['mac'] || 'unknown',
      deviceTime: request.headers['dev-time'] || 'unknown',
      url: request.url,
      method: request.method
    });
  }
  
  done();
});

const startDeviceServer = async () => {
  const devicePort = process.env.DEVICE_PORT || 9001;
  try {
    await fastifyDevice.listen({ port: devicePort });
    logger.info(`Device server listening on port ${devicePort}`);
  } catch (err) {
    logger.error('Failed to start device server', { error: err.message });
    process.exit(1);
  }
};

startDeviceServer();
