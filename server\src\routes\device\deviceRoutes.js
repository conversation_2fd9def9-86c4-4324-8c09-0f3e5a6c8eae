import { <PERSON>ce<PERSON>ontroller } from '../../controllers/deviceController.js';
import { DeviceService } from '../../services/deviceService.js';
import { DataService } from '../../services/dataService.js';
import { TimeService } from '../../services/timeService.js';
import queryLogs from '../../utils/queryLogs.js';

export default async function deviceRoutes(fastify, options) {

    const knex = fastify.knex;

    const deviceService = new DeviceService(knex);
    const dataService = new DataService(knex, deviceService, fastify.log);
    const timeService = new TimeService();

    const deviceController = new DeviceController(
        deviceService,
        dataService,
        timeService
    );

    // sending data (metadata or device data)
    fastify.post('/data', {
        handler: (request, reply) => {
            if (request.headers['dev-time']) {
                return deviceController.handleDevicePost(request, reply);
            } else {
                return deviceController.handleHandshake(request, reply);
            }
        }
    });

    // fetching metadata history>>> tested
    fastify.get('/meta-history/:mac', {
        handler: (request, reply) => deviceController.getMetadataHistory(request, reply)
    });
    
    // Query logs endpoint
    fastify.get('/logs',queryLogs);

    // Query device data from databases
    fastify.get('/query-data', async (request, reply) => {
        try {
            const { 
                devices = [], // array of device MACs to query
                fields = [],  // array of fields to retrieve
                from,         // start timestamp
                to,           // end timestamp
                limit = 1000, // max records per device
                format = 'json' // response format (json or csv)
            } = request.query;            
            
            // Validate required parameters only if one is provided
            if ((from && !to) || (!from && to)) {
                return reply.code(400).send({ error: 'Both from and to parameters must be provided together' });
            }

            // Convert to array if single values provided
            const deviceList = Array.isArray(devices) ? devices : [devices];
            const fieldList = Array.isArray(fields) ? fields : [fields];            // Get data for each device
            const results = {};
            for (const mac of deviceList) {
                // Parse timestamps properly - only convert if they exist and are valid
                const parsedFrom = from && !isNaN(parseInt(from)) ? parseInt(from) : null;
                const parsedTo = to && !isNaN(parseInt(to)) ? parseInt(to) : null;
                const parsedLimit = limit && !isNaN(parseInt(limit)) ? parseInt(limit) : 1000;

                // Get device data from database
                const deviceData = await dataService.queryDeviceData(mac, {
                    fields: fieldList.length > 0 ? fieldList : null,
                    from: parsedFrom,
                    to: parsedTo,
                    limit: parsedLimit
                });
                
                if (deviceData && deviceData.length > 0) {
                    results[mac] = deviceData;
                }
            }

            // Return data in requested format
            if (format === 'csv') {
                // Convert to CSV
                let csvContent = '';
                
                // Create header row with device-prefixed field names
                const headers = ['timestamp'];
                for (const mac of deviceList) {
                    if (results[mac] && results[mac].length > 0) {
                        const dataFields = Object.keys(results[mac][0]).filter(f => f !== 'timestamp');
                        dataFields.forEach(field => headers.push(`${mac}_${field}`));
                    }
                }
                csvContent = headers.join(',') + '\n';
                
                // Create a unified timeline of all timestamps
                const allTimestamps = new Set();
                Object.values(results).forEach(deviceData => {
                    deviceData.forEach(row => allTimestamps.add(row.timestamp));
                });
                
                // Sort timestamps
                const sortedTimestamps = Array.from(allTimestamps).sort();
                
                // Create rows for each timestamp
                sortedTimestamps.forEach(timestamp => {
                    let row = [timestamp];
                    
                    for (const mac of deviceList) {
                        if (!results[mac]) continue;
                        
                        const dataPoint = results[mac].find(d => d.timestamp === timestamp);
                        if (dataPoint) {
                            const dataFields = Object.keys(dataPoint).filter(f => f !== 'timestamp');
                            dataFields.forEach(field => row.push(dataPoint[field]));
                        } else {
                            // Add empty values for missing data points
                            const dataFields = results[mac][0] ? 
                                Object.keys(results[mac][0]).filter(f => f !== 'timestamp') : [];
                            row = row.concat(Array(dataFields.length).fill(''));
                        }
                    }
                    
                    csvContent += row.join(',') + '\n';
                });
                
                return reply
                    .code(200)
                    .header('Content-Type', 'text/csv')
                    .header('Content-Disposition', `attachment; filename="device_data_${from}_${to}.csv"`)
                    .send(csvContent);
            } else {
                // Return JSON
                return reply.code(200).send(results);
            }
        } catch (error) {
            fastify.log.error('Error querying device data:', error);
            return reply.code(500).send({ 
                error: 'Failed to query device data',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });
}


queryDeviceData