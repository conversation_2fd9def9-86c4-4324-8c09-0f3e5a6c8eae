### Query Data Route Test Cases

### 1. Basic query - single device, JSON format
GET http://127.0.0.1:9001/api/device/query-data?devices=00:15:7E:3A:80:03&from=1400435085&to=1712966640&format=json&limit=10

### 2. Query multiple devices - JSON format
GET http://127.0.0.1:9001/api/device/query-data?devices=AA:BB:CC:DD:EE:FF&devices=00:15:7E:3A:80:03&limit=5

### 3. Query with specific fields only
GET http://127.0.0.1:9001/api/device/query-data?devices=AA:BB:CC:DD:EE:FF&fields=temperature&fields=humidity&from=1745952000&to=1745952400&format=json

### 4. Query with limit parameter
GET http://127.0.0.1:9001/api/device/query-data?devices=AA:BB:CC:DD:EE:FF&limit=5


### 5. Query single device - CSV format with proper headers
GET http://127.0.0.1:9001/api/device/query-data?devices=AA:BB:CC:DD:EE:FF&format=csv

### 6 
GET https://geoscada.netmeter.cloud/api/trends?t0=1747540800&t1=1748145600&type=0&resolution=fine