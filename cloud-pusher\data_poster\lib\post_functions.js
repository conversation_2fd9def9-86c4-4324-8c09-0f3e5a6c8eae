import ky from 'ky'

async function postMetadata(serverUrl, metadata, signal) {
    try {
        const response = await ky.post(serverUrl, {
            json: metadata,
            signal: signal,
            timeout: 10000, // 10 secs 
            retry: 3
        })

        console.log(`METADATA posted to ${serverUrl}, status: ${response.status}`)
        return { response }

    } catch (error) {
        if (error.name === 'AbortError') {
            console.log(`Metadata post to ${serverUrl} aborted.`)
        } else {
            console.error(`Error posting metadata to ${serverUrl}:`, error.message)
        }
        throw error
    }
}

async function postDataBatch(data, serverUrl, mac, signal) {
    if (!data || data.length === 0) {
        console.log(`[POST] Skipping empty batch post`);
        return;
    }
    try {
        const response = await ky.post(serverUrl, {
            headers: {
                'mac': mac,
                'dev-time': Math.floor(Date.now() / 1000)
            },
            json: data,
            signal: signal,
            timeout: 10000,  // 10 secs
            retry: 3
        });
        console.log(`[POST-BATCH] Posted ${data.length} items to ${serverUrl}, status: ${response.status}`);
        return response;
    } catch (error) {
        if (error.name === 'AbortError') {
            console.log(`Data batch post to ${serverUrl} aborted.`)
        } else {
            console.error(`[ERROR] Posting batch to ${serverUrl}:`, error.message)
        }
        throw error
    }
}

export { postMetadata, postDataBatch }